{"version": 3, "names": ["Animated", "useLazyRef", "useAnimatedValue", "initialValue", "current", "Value"], "sourceRoot": "../../../src", "sources": ["utils/useAnimatedValue.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,OAAOC,UAAU,MAAM,cAAc;AAErC,eAAe,SAASC,gBAAgBA,CAACC,YAAoB,EAAE;EAC7D,MAAM;IAAEC;EAAQ,CAAC,GAAGH,UAAU,CAAC,MAAM,IAAID,QAAQ,CAACK,KAAK,CAACF,YAAY,CAAC,CAAC;EAEtE,OAAOC,OAAO;AAChB", "ignoreList": []}