const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push('db', 'mp3', 'ttf', 'obj', 'png', 'jpg');

// Add TypeScript support and handle .ts files in node_modules
config.resolver.sourceExts.push('ts', 'tsx');

// Configure transformer to handle TypeScript files and ignore patterns
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('metro-react-native-babel-transformer'),
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
  // Add transform ignore patterns to process TypeScript files in node_modules
  transformIgnorePatterns: [
    'node_modules/(?!(expo-modules-core|expo-.*|@expo/.*|react-native|@react-native/.*|@react-navigation/.*|react-redux|@reduxjs/.*)/)'
  ],
};

// Handle .ts files in node_modules by treating them as source files
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add resolver configuration to handle TypeScript files in dependencies
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Configure Metro to transform TypeScript files in node_modules
config.transformer.unstable_allowRequireContext = true;

// Add watchman ignore patterns to avoid watching unnecessary files
config.watchFolders = [];

// Configure resolver to handle TypeScript extensions properly
config.resolver.sourceExts = [...config.resolver.sourceExts, 'ts', 'tsx', 'js', 'jsx', 'json'];

module.exports = config;