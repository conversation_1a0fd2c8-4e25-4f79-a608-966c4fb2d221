{"version": 3, "names": ["React", "useLayout", "layout", "setLayout", "useState", "height", "width", "measured", "onLayout", "useCallback", "e", "nativeEvent"], "sourceRoot": "../../../src", "sources": ["utils/useLayout.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGH,KAAK,CAACI,QAAQ,CAIvC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC;EAE5C,MAAMC,QAAQ,GAAGR,KAAK,CAACS,WAAW,CAC/BC,CAAoB,IAAK;IACxB,MAAM;MAAEL,MAAM;MAAEC;IAAM,CAAC,GAAGI,CAAC,CAACC,WAAW,CAACT,MAAM;IAE9C,IAAIG,MAAM,KAAKH,MAAM,CAACG,MAAM,IAAIC,KAAK,KAAKJ,MAAM,CAACI,KAAK,EAAE;MACtD;IACF;IAEAH,SAAS,CAAC;MACRE,MAAM;MACNC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EACD,CAACL,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,KAAK,CAC9B,CAAC;EAED,OAAO,CAACJ,MAAM,EAAEM,QAAQ,CAAC;AAC3B", "ignoreList": []}