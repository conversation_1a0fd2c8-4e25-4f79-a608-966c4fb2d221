{"name": "finance-manager-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.2", "@react-navigation/material-top-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "expo": "~53.0.13", "expo-document-picker": "^13.1.6", "expo-file-system": "^18.1.10", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.22.1", "react-native-pager-view": "^6.5.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.16.1", "react-native-safe-area-context": "^5.0.7", "react-native-screens": "^4.4.0", "react-native-svg": "^15.12.0", "react-native-tab-view": "^4.1.1", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}